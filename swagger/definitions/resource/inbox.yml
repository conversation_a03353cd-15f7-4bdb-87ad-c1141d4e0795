type: object
properties:
  id:
    type: number
    description: ID of the inbox
  name:
    type: string
    description: The name of the inbox
  website_url:
    type: string
    description: Website URL
  channel_type:
    type: string
    description: The type of the inbox
  avatar_url:
    type: string
    description: The avatar image of the inbox
  widget_color:
    type: string
    description: Widget Color used for customization of the widget
  website_token:
    type: string
    description: Website Token
  enable_auto_assignment:
    type: boolean
    description: The flag which shows whether Auto Assignment is enabled or not
  web_widget_script:
    type: string
    description: <PERSON>ript used to load the website widget
  welcome_title:
    type: string
    description: Welcome title to be displayed on the widget
  welcome_tagline:
    type: string
    description: Welcome tagline to be displayed on the widget
  greeting_enabled:
    type: boolean
    description: The flag which shows whether greeting is enabled
  greeting_message:
    type: string
    description: A greeting message when the user starts the conversation
  channel_id:
    type: number
    description: ID of the channel this inbox belongs to
  working_hours_enabled:
    type: boolean
    description: The flag which shows whether working hours feature is enabled
  enable_email_collect:
    type: boolean
    description: The flag to enable collecting email from contacts
  csat_survey_enabled:
    type: boolean
    description: The flag to enable CSAT survey
  auto_assignment_config:
    type: object
    description: Configuration settings for auto assignment
  out_of_office_message:
    type: string
    description: Message to show when agents are out of office
  working_hours:
    type: array
    description: Configuration for working hours of the inbox
    items:
      type: object
      properties:
        day_of_week:
          type: number
          description: Day of the week (0-6, where 0 is Sunday)
        closed_all_day:
          type: boolean
          description: Whether the inbox is closed for the entire day
        open_hour:
          type: number
          description: Hour when inbox opens (0-23)
        open_minutes:
          type: number
          description: Minutes of the hour when inbox opens (0-59)
        close_hour:
          type: number
          description: Hour when inbox closes (0-23)
        close_minutes:
          type: number
          description: Minutes of the hour when inbox closes (0-59)
        open_all_day:
          type: boolean
          description: Whether the inbox is open for the entire day
  timezone:
    type: string
    description: Timezone configuration for the inbox
  callback_webhook_url:
    type: string
    description: Webhook URL for callbacks
  allow_messages_after_resolved:
    type: boolean
    description: Whether to allow messages after a conversation is resolved
  lock_to_single_conversation:
    type: boolean
    description: Whether to lock a contact to a single conversation
  sender_name_type:
    type: string
    description: Type of sender name to display (e.g., friendly)
  business_name:
    type: string
    description: Business name associated with the inbox
  hmac_mandatory:
    type: boolean
    description: Whether HMAC verification is mandatory
  selected_feature_flags:
    type: object
    description: Selected feature flags for the inbox
  reply_time:
    type: string
    description: Expected reply time
  messaging_service_sid:
    type: string
    description: Messaging service SID for SMS providers
  phone_number:
    type: string
    description: Phone number associated with the inbox
  medium:
    type: string
    description: Medium of communication (e.g., sms, email)
  provider:
    type: string
    description: Provider of the channel
