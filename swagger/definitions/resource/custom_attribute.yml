type: object
properties:
  id:
    type: integer
    description: Identifier
  attribute_display_name:
    type: string
    description: Attribute display name
  attribute_display_type:
    type: string
    description: Attribute display type (text, number, currency, percent, link, date, list, checkbox)
  attribute_description:
    type: string
    description: Attribute description
  attribute_key:
    type: string
    description: Attribute unique key value
  regex_pattern:
    type: string
    description: Regex pattern
  regex_cue:
    type: string
    description: Regex cue
  attribute_values:
    type: string
    description: Attribute values
  attribute_model:
    type: string
    description: Attribute type(conversation_attribute/contact_attribute)
  default_value:
    type: string
    description: Attribute default value
  created_at:
    type: string
    description: The date and time when the custom attribute was created
  updated_at:
    type: string
    description: The date and time when the custom attribute was updated
