type: object
properties:
  name:
    type: string
    description: Name of the user
    example: '<PERSON>'
  display_name:
    type: string
    description: Display name of the user
    example: '<PERSON>'
  email:
    type: string
    description: Email of the user
    example: '<EMAIL>'
  password:
    type: string
    description: Password must contain uppercase, lowercase letters, number and a special character
    example: 'Password2!'
  custom_attributes:
    type: object
    description: Custom attributes you want to associate with the user
    example: {}
