type: object
properties:
  identifier:
    type: string
    description: External identifier of the contact
    example: '1234567890'
  identifier_hash:
    type: string
    description: Identifier hash prepared for HMAC authentication
    example: 'e93275d4eba0e5679ad55f5360af00444e2a888df9b0afa3e8b691c3173725f9'
  email:
    type: string
    description: Email of the contact
    example: <EMAIL>
  name:
    type: string
    description: Name of the contact
    example: Alice
  phone_number:
    type: string
    description: Phone number of the contact
    example: '+123456789'
  avatar:
    type: string
    format: binary
    description: Send the form data with the avatar image binary or use the avatar_url
  custom_attributes:
    type: object
    description: Custom attributes of the customer
    example: {}
