type: object
properties:
  name:
    type: string
    description: name of the contact
    example: Alice
  email:
    type: string
    description: email of the contact
    example: <EMAIL>
  blocked:
    type: boolean
    description: whether the contact is blocked or not
    example: false
  phone_number:
    type: string
    description: phone number of the contact
    example: '+123456789'
  avatar:
    type: string
    format: binary
    description: Send the form data with the avatar image binary or use the avatar_url
  avatar_url:
    type: string
    description: The url to a jpeg, png file for the contact avatar
    example: https://example.com/avatar.png
  identifier:
    type: string
    description: A unique identifier for the contact in external system
    example: '1234567890'
  additional_attributes:
    type: object
    description: An object where you can store additional attributes for contact. example {"type":"customer", "age":30}
    example: { 'type': 'customer', 'age': 30 }
  custom_attributes:
    type: object
    description: An object where you can store custom attributes for contact. example {"type":"customer", "age":30}, this should have a valid custom attribute definition.
    example: {}
