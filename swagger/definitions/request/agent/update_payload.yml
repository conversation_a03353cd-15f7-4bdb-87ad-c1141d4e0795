type: object
required:
  - role
properties:
  role:
    type: string
    enum: ['agent', 'administrator']
    description: Whether its administrator or agent
    example: 'agent'
  availability_status:
    type: string
    enum: ['available', 'busy', 'offline']
    description: The availability status of the agent.
    example: 'available'
  auto_offline:
    type: boolean
    description: Whether the availability status of agent is configured to go offline automatically when away.
    example: true
