tags:
  - Custom Attributes
operationId: add-new-custom-attribute-to-account
summary: Add a new custom attribute
description: Add a new custom attribute to account
security:
  - userApiKey: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/custom_attribute_create_update_payload'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/custom_attribute'
  '403':
    description: Access denied
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
