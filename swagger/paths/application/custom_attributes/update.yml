tags:
  - Custom Attributes
operationId: update-custom-attribute-in-account
summary: Update custom attribute in Account
description: Update a custom attribute in account
security:
  - userApiKey: []
parameters:
  - in: path
    name: id
    schema:
      type: integer
    required: true
    description: The ID of the custom attribute to be updated.
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/custom_attribute_create_update_payload'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          description: 'The updated custom attribute'
          $ref: '#/components/schemas/custom_attribute'
  '404':
    description: Agent not found
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '403':
    description: Access denied
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
