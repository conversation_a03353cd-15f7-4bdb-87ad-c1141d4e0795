tags:
  - Webhooks
operationId: delete-a-webhook
summary: Delete a webhook
security:
  - userApiKey: []
description: Delete a webhook from the account
responses:
  '200':
    description: Success
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          type: object
  '404':
    description: The webhook does not exist in the account
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
