post:
  tags:
    - Inboxes
  operationId: inboxCreation
  summary: Create an inbox
  description: You can create more than one website inbox in each account
  security:
    - userApiKey: []
  parameters:
    - $ref: '#/components/parameters/account_id'
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/inbox_create_payload'
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/inbox'
    '404':
      description: Inbox not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
    '403':
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
