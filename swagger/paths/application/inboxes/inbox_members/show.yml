tags:
  - Inboxes
operationId: get-inbox-members
summary: List Agents in Inbox
description: Get Details of Agents in an Inbox
security:
  - userApiKey: []
parameters:
  - $ref: '#/components/parameters/inbox_id'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          type: object
          properties:
            payload:
              type: array
              description: 'Array of all active agents'
              items:
                $ref: '#/components/schemas/agent'
  '404':
    description: Inbox not found
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '403':
    description: Access denied
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
