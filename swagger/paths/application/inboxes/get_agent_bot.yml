get:
  tags:
    - Inboxes
  operationId: getInboxAgentBot
  summary: Show Inbox Agent Bot
  description: See if an agent bot is associated to the Inbox
  security:
    - userApiKey: []
  parameters:
    - $ref: '#/components/parameters/account_id'
    - name: id
      in: path
      schema:
        type: number
      description: ID of the inbox
      required: true
  responses:
    '204':
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/agent_bot'
    '404':
      description: Inbox not found, Agent bot not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
    '403':
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
