tags:
  - Conversations
operationId: toggle-priority-of-a-conversation
summary: Toggle Priority
description: Toggles the priority of conversation
security:
  - userApi<PERSON>ey: []
  - agentBotApiKey: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        required:
          - priority
        properties:
          priority:
            type: string
            enum: ['urgent', 'high', 'medium', 'low', 'none']
            description: 'The priority of the conversation'
            example: 'high'
responses:
  '200':
    description: Success
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '404':
    description: Conversation not found
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
