parameters:
  - $ref: '#/components/parameters/account_id'

get:
  tags:
    - Conversations
  operationId: conversationListMeta
  description: Get open, unassigned and all Conversation counts
  summary: Get Conversation Counts
  security:
    - userApiKey: []
  parameters:
    - name: status
      in: query
      schema:
        type: string
        enum: ['all', 'open', 'resolved', 'pending', 'snoozed']
        default: 'open'
      description: Filter by conversation status.
    - name: q
      in: query
      schema:
        type: string
      description: Filters conversations with messages containing the search term
    - name: inbox_id
      in: query
      schema:
        type: integer
    - name: team_id
      in: query
      schema:
        type: integer
    - name: labels
      in: query
      schema:
        type: array
        items:
          type: string

  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              meta:
                type: object
                properties:
                  mine_count:
                    type: number
                  unassigned_count:
                    type: number
                  assigned_count:
                    type: number
                  all_count:
                    type: number
    '400':
      description: Bad Request Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
