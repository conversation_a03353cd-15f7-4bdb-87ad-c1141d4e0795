tags:
  - Teams
operationId: add-new-agent-to-team
summary: Add a New Agent
description: Add a new Agent to Team
security:
  - userApiKey: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        required:
          - user_ids
        properties:
          user_ids:
            type: array
            items:
              type: integer
            description: IDs of users to be added to the team
            example: [1]
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          type: array
          description: 'Array of all active agents'
          items:
            $ref: '#/components/schemas/agent'
  '403':
    description: Access denied
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '404':
    description: Team not found
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '422':
    description: User must exist
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
