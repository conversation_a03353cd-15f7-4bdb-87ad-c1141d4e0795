tags:
  - Automation Rule
operationId: get-details-of-a-single-automation-rule
summary: Get a automation rule details
description: Get the details of a automation rule in the account
security:
  - userApiKey: []
parameters:
  - in: path
    name: id
    schema:
      type: integer
    required: true
    description: The ID of the automation rule to be updated.
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/automation_rule'
        example:
          payload:
            id: 90
            account_id: 1
            name: "add-label-bug-if-message-contains-bug"
            description: "add-label-bug-if-message-contains-bug"
            event_name: "message_created"
            conditions:
              - values: ["incoming"]
                attribute_key: "message_type"
                query_operator: "and"
                filter_operator: "equal_to"
              - values: ["bug"]
                attribute_key: "content"
                filter_operator: "contains"
            actions:
              - action_name: "add_label"
                action_params: ["bugs", "support-query"]
            created_on: **********
            active: true
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '404':
    description: The given rule ID does not exist in the account
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
