tags:
  - Custom Filters
operationId: update-a-custom-filter
summary: Update a custom filter
security:
  - userApiKey: []
description: Update a custom filter's attributes
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/custom_filter_create_update_payload'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/custom_filter'
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
