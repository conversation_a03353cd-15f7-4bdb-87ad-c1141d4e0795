tags:
  - Account AgentBots
operationId: delete-an-account-agent-bot
summary: Delete an AgentBot
description: Delete an AgentBot from the account
security:
  - userApiKey: []
responses:
  200:
    description: Success
  401:
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  404:
    description: The agent bot does not exist in the account
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
