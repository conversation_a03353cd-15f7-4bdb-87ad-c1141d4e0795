tags:
  - Agents
operationId: get-account-agents
summary: List Agents in Account
description: Get Details of Agents in an Account
security:
  - userApiKey: []
responses:
  200:
    description: Success
    content:
      application/json:
        schema:
          type: array
          description: 'Array of all active agents'
          items:
            $ref: '#/components/schemas/agent'
  403:
    description: Access denied
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
