tags:
  - Teams
operationId: create-a-team
summary: Create a team
security:
  - userApi<PERSON>ey: []
description: Create a team in the account
parameters:
  - $ref: '#/components/parameters/account_id'
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/team_create_update_payload'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/team'
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
