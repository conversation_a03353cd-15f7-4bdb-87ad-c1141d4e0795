tags:
  - Reports
operationId: list-all-conversation-statistics-summary
summary: Get Account reports summary
security:
  - userApiKey: []
description: Get Account reports summary for a specific type and date range
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          description: 'Object of summary metrics'
          $ref: '#/components/schemas/account_summary'
  '404':
    description: reports not found
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '403':
    description: Access denied
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
