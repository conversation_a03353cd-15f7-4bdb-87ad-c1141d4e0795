tags:
  - Account
operationId: update-account
summary: Update account
description: Update account details, settings, and custom attributes
security:
  - userApiKey: []
parameters:
  - $ref: '#/components/parameters/account_id'
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/account_update_payload'
    application/x-www-form-urlencoded:
      schema:
        $ref: '#/components/schemas/account_update_payload'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/account_detail'
  '401':
    description: Unauthorized (requires administrator role)
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '404':
    description: Account not found
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '422':
    description: Validation error
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'