get:
  tags:
    - Contacts
  operationId: contactSearch
  description: Search the resolved contacts using a search key, currently supports email search (Page size = 15). Resolved contacts are the ones with a value for identifier, email or phone number
  summary: Search Contacts
  security:
    - userApiKey: []
  parameters:
    - $ref: '#/components/parameters/account_id'
    - name: q
      in: query
      schema:
        type: string
      description: Search using contact `name`, `identifier`, `email` or `phone number`
    - $ref: '#/components/parameters/contact_sort_param'
    - $ref: '#/components/parameters/page'
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/contacts_list_response'
    '401':
      description: Authentication error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
