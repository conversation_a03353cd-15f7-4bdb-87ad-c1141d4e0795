parameters:
  - $ref: '#/components/parameters/account_id'
  - name: id
    in: path
    required: true
    schema:
      type: number
    description: ID of the contact

get:
  tags:
    - Contacts
  operationId: contactConversations
  summary: Contact Conversations
  description: Get conversations associated with that contact
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: number
      description: ID of the contact
  security:
    - userApi<PERSON>ey: []
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/contact_conversations_response'
    '404':
      description: Contact not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
    '403':
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
