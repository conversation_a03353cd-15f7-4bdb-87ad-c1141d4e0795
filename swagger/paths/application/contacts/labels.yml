parameters:
  - $ref: '#/components/parameters/account_id'
  - name: id
    in: path
    required: true
    schema:
      type: number
    description: ID of the contact

get:
  tags:
    - Contact Labels
  operationId: list-all-labels-of-a-contact
  summary: List Labels
  description: Lists all the labels of a contact
  security:
    - userApiKey: []
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/contact_labels'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
    '404':
      description: Contact not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'

post:
  tags:
    - Contact Labels
  operationId: contact-add-labels
  summary: Add Labels
  description: Add labels to a contact. Note that this API would overwrite the existing list of labels associated to the conversation.
  security:
    - userApiKey: []
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          required:
            - labels
          properties:
            labels:
              type: array
              description: Array of labels (comma-separated strings)
              items:
                type: string
              example: ['support', 'billing']
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/contact_labels'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
    '404':
      description: Contact not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
