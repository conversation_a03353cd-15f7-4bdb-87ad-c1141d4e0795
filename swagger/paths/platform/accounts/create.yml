tags:
  - Accounts
operationId: create-an-account
summary: Create an Account
description: Create an Account
security:
  - platformAppApiKey: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/account_create_update_payload'
responses:
  200:
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/platform_account'
  401:
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
