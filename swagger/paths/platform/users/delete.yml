tags:
  - Users
operationId: delete-a-user
summary: Delete a User
description: Delete a User
security:
  - platformAppApiKey: []
responses:
  '200':
    description: Success
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
  '404':
    description: The user does not exist
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
