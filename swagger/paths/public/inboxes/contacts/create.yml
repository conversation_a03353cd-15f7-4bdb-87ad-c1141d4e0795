tags:
  - Contacts API
operationId: create-a-contact
summary: Create a contact
description: Create a contact
security: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '#/components/schemas/public_contact_create_update_payload'
responses:
  '200':
    description: Success
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/public_contact'
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/bad_request_error'
