openapi: '3.0.4'
info:
  title: Chatwoot - Platform API
  description: Platform API endpoints for Chatwoot
  version: 1.1.0
  termsOfService: https://www.chatwoot.com/terms-of-service/
  contact:
    email: <EMAIL>
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT
servers:
  - url: https://app.chatwoot.com/
tags:
  - name: Accounts
    description: Platform account management
  - name: Account Users
    description: Manage users within platform accounts
  - name: AgentBots
    description: Manage agent bots on the platform
  - name: Users
    description: Platform user management
paths:
  $ref: ../paths/index.yml
components:
  schemas:
    $ref: ../definitions/index.yml
  parameters:
    $ref: ../parameters/index.yml
  securitySchemes:
    userApiKey:
      type: api<PERSON><PERSON>
      in: header
      name: api_access_token
      description: This token can be obtained by visiting the profile page or via rails console. Provides access to endpoints based on the user permissions levels.
    platformAppApiKey:
      type: apiKey
      in: header
      name: api_access_token
      description: This token can be obtained by the system admin after creating a platformApp. This token should be used to provision agent bots, accounts, users and their roles. 