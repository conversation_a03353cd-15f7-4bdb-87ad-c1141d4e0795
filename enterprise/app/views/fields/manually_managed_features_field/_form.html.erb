<%
  # Get all feature names and their display names
  all_feature_display_names = SuperAdmin::AccountFeaturesHelper.feature_display_names
  
  # Business and Enterprise plan features only
  premium_features = Enterprise::Billing::HandleStripeEventService::BUSINESS_PLAN_FEATURES + 
                     Enterprise::Billing::HandleStripeEventService::ENTERPRISE_PLAN_FEATURES
  
  # Get only premium features with display names
  premium_features_with_display = premium_features.map do |feature|
    [feature, all_feature_display_names[feature] || feature.humanize]
  end.sort_by { |_, display_name| display_name }
  
  # Get already selected features
  selected_features = field.selected_features
%>

<div class="field-unit__label">
  <%= f.label :manually_managed_features %>
</div>
<div class="field-unit__field feature-container">
  <p class="text-gray-400 text-xs italic mb-4">Features that remain enabled even when account plan is downgraded</p>
  
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <% premium_features_with_display.each do |feature_key, display_name| %>
      <div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm outline outline-1 outline-n-container">
        <span class="text-sm text-slate-700"><%= display_name %></span>
        <span>
          <%= check_box_tag "account[manually_managed_features][]", 
                         feature_key, 
                         selected_features.include?(feature_key), 
                         class: "h-4 w-4 rounded border-slate-300 text-violet-600 focus:ring-violet-600" %>
        </span>
      </div>
    <% end %>
  </div>
  
  <hr class="my-8 border-t border-n-weak">
  
  <%= hidden_field_tag "account[manually_managed_features][]", "", id: nil %>
</div>