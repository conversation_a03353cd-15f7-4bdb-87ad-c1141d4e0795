#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
ar:
  hello: 'مرحباً بالعالم'
  messages:
    reset_password_success: تم إرسال طلب إعادة تعيين كلمة المرور. يرجى مراجعة بريدك الإلكتروني للحصول على التعليمات.
    reset_password_failure: المعذرة! لم نتمكن من العثور على أي مستخدم بعنوان البريد الإلكتروني المحدد.
    inbox_deletetion_response: سيتم معالجة طلب حذف صندوق الوارد الخاص بك في بعض الوقت.
  errors:
    validations:
      presence: يجب ألا يكون فارغاً
    webhook:
      invalid: أحداث غير صالحة
    signup:
      disposable_email: نحن لا نسمح باسخدام عناوين البريد الإلكتروني المؤقتة
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: لقد قمت بإدخال عنوان بريد إلكتروني غير صالح
      email_already_exists: 'لقد قمت بالفعل بتسجيل حساب سابقاً بالعنوان %{email}'
      invalid_params: 'غير صالح، الرجاء التحقق من خانات التسجيل وحاول مرة أخرى'
      failed: فشلت عملية التسجيل
    data_import:
      data_type:
        invalid: نوع البيانات غير صالح
    contacts:
      import:
        failed: الملف فارغ
      export:
        success: سنقوم بإعلامك بمجرد أن يكون ملف تصدير جهات الاتصال جاهزاً للعرض.
      email:
        invalid: إيميل غير صالح
      phone_number:
        invalid: يجب أن يكون بصيغة e164
    categories:
      locale:
        unique: يجب أن تكون فريدة من نوعها في الفئة والبوابة
    dyte:
      invalid_message_type: 'نوع الرسالة غير صالح. الإجراء غير مسموح به'
    slack:
      invalid_channel_id: 'قناة Slack غير صحيحة. الرجاء المحاولة مرة أخرى'
    inboxes:
      imap:
        socket_error: الرجاء التحقق من اتصال الشبكة وعنوان IMAP ثم حاول مرة أخرى.
        no_response_error: الرجاء التحقق من بيانات اعتماد IMAP ثم حاول مرة أخرى.
        host_unreachable_error: المضيف لا يمكن الوصول إليه، الرجاء التحقق من عنوان IMAP ومنفذ IMAP ثم حاول مرة أخرى.
        connection_timed_out_error: انتهت مهلة الاتصال لـ %{address}:%{port}
        connection_closed_error: تم إغلاق الاتصال.
      validations:
        name: لا ينبغي أن تبدأ أو تنتهي بالرموز، ولا ينبغي أن يكون أقل من > / \ أحرف @ .
    custom_filters:
      number_of_records: تم الوصول إلى الحد الأقصى. الحد الأقصى لعدد عوامل التصفية المخصصة المسموح به للمستخدم لكل حساب هو 50.
      invalid_attribute: مفتاح السمة غير صالح - [%{key}]. يجب أن يكون المفتاح واحد من [%{allowed_keys}] أو سمة مخصصة محددة في الحساب.
      invalid_operator: مشغل غير صالح. المشغل المسموح به لـ %{attribute_name} هو [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: قيمة غير صالحة. القيم المقدمة ل %{attribute_name} غير صالحة
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: فترة التبليغ %{since} إلى %{until}
    utc_warning: التقرير الذي تم إنشاؤه في التوقيت العالمي الموحّد
    agent_csv:
      agent_name: اسم الوكيل
      conversations_count: المحادثات المعينة
      avg_first_response_time: متوسط وقت الاستجابة الأولى
      avg_resolution_time: متوسط وقت الحل
      resolution_count: عدد مرات الإغلاق
      avg_customer_waiting_time: متوسط وقت انتظار العميل
    inbox_csv:
      inbox_name: اسم صندوق الوارد
      inbox_type: نوع صندوق البريد
      conversations_count: عدد المحادثات
      avg_first_response_time: متوسط وقت الرد الأول
      avg_resolution_time: متوسط وقت الحل
    label_csv:
      label_title: الوسم
      conversations_count: عدد المحادثات
      avg_first_response_time: متوسط وقت الرد الأول
      avg_resolution_time: متوسط وقت الحل
      avg_reply_time: Avg reply time
      resolution_count: عدد مرات الإغلاق
    team_csv:
      team_name: اسم الفريق
      conversations_count: عدد المحادثات
      avg_first_response_time: متوسط وقت الرد الأول
      avg_resolution_time: متوسط وقت الحل
      resolution_count: عدد مرات الإغلاق
      avg_customer_waiting_time: متوسط وقت انتظار العميل
    conversation_traffic_csv:
      timezone: منطقة زمنية
    sla_csv:
      conversation_id: معرف المحادثة
      sla_policy_breached: سياسة مستوى الخدمة
      assignee: المكلَّف
      team: الفريق
      inbox: صندوق الوارد
      labels: الوسوم
      conversation_link: رابط للمحادثة
      breached_events: أحداث الخرق
    default_group_by: اليوم
    csat:
      headers:
        contact_name: اسم جهة الاتصال
        contact_email_address: عنوان البريد الإلكتروني الخاص بجهة الاتصال
        contact_phone_number: رقم هاتف جهة الاتصال
        link_to_the_conversation: رابط إلى المحادثة
        agent_name: اسم الوكيل
        rating: التقييم
        feedback: التعليق على الملاحظات
        recorded_at: تاريخ التسجيل
  notifications:
    notification_title:
      conversation_creation: 'تم إنشاء محادثة (#%{display_id}) في %{inbox_name}'
      conversation_assignment: 'تم تعيين محادثة (#%{display_id}) لك'
      assigned_conversation_new_message: 'تم إنشاء رسالة جديدة في المحادثة (#%{display_id})'
      conversation_mention: 'تمت الإشارة إليك في المحادثة من قبل (#%{display_id})'
      sla_missed_first_response: 'هدف سياسة خدمة أول رد مفقود للمحادثة (#%{display_id})'
      sla_missed_next_response: 'هدف سياسة خدمة الرد القادم مفقود للمحادثة (#%{display_id})'
      sla_missed_resolution: 'هدف سياسة خدمة أول حل مفقود للمحادثة (#%{display_id})'
    attachment: 'المرفقات'
    no_content: 'لا يوجد محتوى'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: 'أشار %{story_sender} إليك في القصة: '
      instagram_deleted_story_content: هذه القصة لم تعد متاحة.
      deleted: تم حذف هذه الرسالة
      whatsapp:
        list_button_label: 'Choose an item'
      delivery_status:
        error_code: 'رمز الخطأ: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'تم تحديث حالة المحادثة لـ"مغلقة" بواسطة %{user_name}'
        contact_resolved: 'تم حل المحادثة بواسطة %{contact_name}'
        open: 'تم إعادة فتح المحادثة بواسطة %{user_name}'
        pending: 'تم تحديث حالة المحادثة لـ"معلقة" بواسطة %{user_name}'
        snoozed: 'تم تأجيل المحادثة بواسطة %{user_name}'
        auto_resolved_days: 'تم وضع علامة على المحادثة كمحلولة من قبل النظام بسبب %{count} أيام من عدم النشاط'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: أعاد النظام فتح المحادثة بسبب رسالة واردة جديدة.
      priority:
        added: '%{user_name} حدد الأولوية إلى %{new_priority}'
        updated: '%{user_name} غيّر الأولوية من %{old_priority} إلى %{new_priority}'
        removed: '%{user_name} أزال الأولوية'
      assignee:
        self_assigned: '%{user_name} تم تعيينه تلقائياً لهذه المحادثة'
        assigned: 'تم إسنادها إلى %{assignee_name} بواسطة %{user_name}'
        removed: 'المحادثة غير مسندة بواسطة %{user_name}'
      team:
        assigned: 'تم إسنادها إلى %{team_name} بواسطة %{user_name}'
        assigned_with_assignee: 'تم تعيينه إلى %{assignee_name} عبر %{team_name} بواسطة %{user_name}'
        removed: 'إلغاء التعيين من %{team_name} بواسطة %{user_name}'
      labels:
        added: '%{user_name} أضاف %{labels}'
        removed: '%{user_name} أزال %{labels}'
      sla:
        added: '%{user_name} أضاف سياسة مستوى الخدمة %{sla_name}'
        removed: '%{user_name} أزال سياسة مستوى الخدمة %{sla_name}'
      linear:
        issue_created: 'Linear issue %{issue_id} was created by %{user_name}'
        issue_linked: 'Linear issue %{issue_id} was linked by %{user_name}'
        issue_unlinked: 'Linear issue %{issue_id} was unlinked by %{user_name}'
      csat:
        not_sent_due_to_messaging_window: 'CSAT survey not sent due to outgoing message restrictions'
      muted: '%{user_name} كتم صوت المحادثة'
      unmuted: '%{user_name} قام بإلغاء كتم المحادثة'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} يرد عادة خلال بضع ساعات.'
      ways_to_reach_you_message_body: 'زودنا بوسيلة للتواصل معك.'
      email_input_box_message_body: 'احصل على الإشعارات في البريد الإلكتروني'
      csat_input_message_body: 'الرجاء تقييم المحادثة'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} من %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} من %{inbox_name} <%{reply_email}>'
          friendly_name: '%{sender_name} من %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} من %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'رسائل جديدة في هذه المحادثة'
      transcript_subject: 'نص المحادثة'
    survey:
      response: 'الرجاء تقييم هذه المحادثة، %{link}'
  contacts:
    online:
      delete: '%{contact_name} متصل، يرجى المحاولة مرة أخرى لاحقاً'
  integration_apps:
    #Note: webhooks and dashboard_apps don't need short_description as they use different modal components
    dashboard_apps:
      name: 'تطبيقات لوحة التحكم'
      description: 'تسمح لك تطبيقات لوحة التحكم بإنشاء وتضمين التطبيقات التي تعرض معلومات المستخدم أو الطلبات أو سجل الدفع، مما يوفر المزيد من السياق لوكلاء دعم العملاء الخاص بك.'
    dyte:
      name: 'Dyte'
      short_description: 'Start video/voice calls with customers directly from Chatwoot.'
      description: 'Dyte هو منتج يدمج وظائف الصوت والفيديو في تطبيقك. مع هذا الدمج، يمكن لوكلائك بدء مكالمات الفيديو/الصوت مع عملائك مباشرة من Chatwoot.'
      meeting_name: 'بدأ %{agent_name} اجتماعاً'
    slack:
      name: 'Slack'
      short_description: 'Receive notifications and respond to conversations directly in Slack.'
      description: "دمج Chatwoot مع Slack للحفاظ على مزامنة فريقك. هذا التكامل يسمح لك باستلام إشعارات للمحادثات الجديدة والرد عليها مباشرة داخل واجهة Slacks."
    webhooks:
      name: 'Webhook'
      description: 'أحداث Webhook توفر تحديثات في الوقت الحقيقي حول الأنشطة في حساب Chatwoot الخاص بك. يمكنك الاشتراك في الأحداث المفضلة الخاصة بك، وسترسل Chatwoot لك اتصالات HTTP مع التحديثات.'
    dialogflow:
      name: 'Dialogflow'
      short_description: 'Build chatbots to handle initial queries before transferring to agents.'
      description: 'بناء روبوتات الدردشة مع حركة الاتصال ودمجها بسهولة في صندوق الوارد الخاص بك. يمكن لهذه الروبوتات التعامل مع الاستفسارات الأولية قبل نقلها إلى وكيل خدمة العملاء.'
    google_translate:
      name: 'ترجمة Google'
      short_description: 'Automatically translate customer messages for agents.'
      description: "دمج ترجمة جوجل لمساعدة الوكلاء على ترجمة رسائل العملاء بسهولة. هذا الدمج يكشف تلقائياً اللغة ويحولها إلى اللغة المفضلة لدى الوكيل أو المدير."
    openai:
      name: 'OpenAI'
      short_description: 'AI-powered reply suggestions, summarization, and message enhancement.'
      description: 'الاستفادة من قوة نماذج اللغات الكبيرة من OpenAI مع ميزات مثل اقتراحات الرد، التلخيص، إعادة صياغة الرسائل، التحقق الإملائي، تصنيف البطاقات.'
    linear:
      name: 'Linear'
      short_description: 'Create and link Linear issues directly from conversations.'
      description: 'إنشاء مشكلات في Linear مباشرة من نافذة المحادثة الخاصة بك. بدلاً من ذلك، قم بربط مشكلات Linear القائمة من أجل عملية تتبع أكثر تبسيطاً وكفاءة.'
    shopify:
      name: 'Shopify'
      short_description: 'Access order details and customer data from your Shopify store.'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
    copilot:
      using_tool: 'Using tool %{function_name}'
      completed_tool_call: 'Completed %{function_name} tool call'
      invalid_tool_call: 'Invalid tool call'
      tool_not_available: 'Tool not available'
  public_portal:
    search:
      search_placeholder: البحث عن مقالة حسب العنوان أو الجسم...
      empty_placeholder: لم يتم العثور على النتائج.
      loading_placeholder: جاري البحث...
      results_title: نتائج البحث
    toc_header: 'في هذه الصفحة'
    hero:
      sub_title: ابحث عن المقالات هنا أو تصفح الفئات أدناه.
    common:
      home: الرئيسية
      last_updated_on: آخر تحديث في %{last_updated_on}
      view_all_articles: عرض الكل
      article: مقال
      articles: المقالات
      author: المؤلف
      authors: المؤلفون
      other: آخر
      others: الآخرين
      by: بواسطة
      no_articles: لا توجد مقالات
    footer:
      made_with: صنع بـ
    header:
      go_to_homepage: الموقع الإلكتروني
      visit_website: Visit website
      appearance:
        system: النظام
        light: فاتح
        dark: مظلم
      featured_articles: المقالات المميزة
      uncategorized: غير مصنف
    404:
      title: لم يتم العثور على الصفحة
      description: لم نتمكن من العثور على الصفحة التي تبحث عنها.
      back_to_home: الذهاب إلى الصفحة الرئيسية
  slack_unfurl:
    fields:
      name: الاسم
      email: البريد الإلكتروني
      phone_number: هاتف
      company_name: المنشأة
      inbox_name: صندوق الوارد
      inbox_type: نوع صندوق الوارد
    button: فتح المحادثة
  time_units:
    days:
      zero: '%{count} يوم'
      one: '%{count} يوم'
      two: '%{count} يوم'
      few: '%{count} أيام'
      many: '%{count} أيام'
      other: '%{count} يوم'
    hours:
      zero: '%{count} ساعة'
      one: '%{count} ساعة'
      two: '%{count} ساعات'
      few: '%{count} ساعات'
      many: '%{count} ساعة'
      other: '%{count} ساعة'
    minutes:
      zero: '%{count} دقيقة'
      one: '%{count} دقيقة'
      two: '%{count} دقائق'
      few: '%{count} دقائق'
      many: '%{count} دقيقة'
      other: '%{count} دقيقة'
    seconds:
      zero: '%{count} ثانية'
      one: '%{count} ثانية'
      two: '%{count} ثواني'
      few: '%{count} ثواني'
      many: '%{count} ثانية'
      other: '%{count} ثانية'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[لا يوجد محتوى]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
