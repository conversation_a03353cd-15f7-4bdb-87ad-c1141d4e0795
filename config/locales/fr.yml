#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
fr:
  hello: 'Bonjour le monde'
  messages:
    reset_password_success: Super ! La demande de réinitialisation du mot de passe a réussi. Consultez vos e-mails pour obtenir des instructions.
    reset_password_failure: Oh oh ! Nous n'avons trouvé aucun utilisateur avec le courriel spécifié.
    inbox_deletetion_response: Votre demande de suppression de la boîte de réception sera traitée dans un certain délai.
  errors:
    validations:
      presence: Ne peut être vide
    webhook:
      invalid: Événements non valides
    signup:
      disposable_email: Nous n'autorisons pas les courriels jetables
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: Vous avez entré un courriel non valide
      email_already_exists: 'Vous avez déjà créé un compte avec %{email}'
      invalid_params: 'Invalid, please check the signup paramters and try again'
      failed: L'inscription a échoué
    data_import:
      data_type:
        invalid: Type de données incorrect
    contacts:
      import:
        failed: Le fichier est vide
      export:
        success: Nous vous informerons lorsque le fichier d'exportation des contacts sera prêt à être affiché.
      email:
        invalid: Email non valide
      phone_number:
        invalid: Doit être au format e164
    categories:
      locale:
        unique: Doit être unique dans la catégorie et le portail
    dyte:
      invalid_message_type: 'Type de message invalide. Action non autorisée'
    slack:
      invalid_channel_id: 'Canal Slack invalide. Veuillez réessayer'
    inboxes:
      imap:
        socket_error: Veuillez vérifier la connexion, l'adresse IMAP et réessayez.
        no_response_error: Veuillez vérifier les identifiants IMAP et réessayez.
        host_unreachable_error: Hôte injoignable, veuillez vérifier l'adresse IMAP, le port IMAP et réessayer.
        connection_timed_out_error: La connexion a expiré pour %{address}:%{port}
        connection_closed_error: Connexion fermée.
      validations:
        name: 'ne doit pas commencer ou se terminer par des symboles, et ne doit pas comporter les caractères suivants : "< > / \ @".'
    custom_filters:
      number_of_records: Limite atteinte. Le nombre maximum de filtres personnalisés autorisés pour un utilisateur par compte est de 50.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Période de rapport %{since} à %{until}
    utc_warning: Le rapport généré est dans le fuseau horaire UTC
    agent_csv:
      agent_name: Nom de l'agent
      conversations_count: Conversations assignées
      avg_first_response_time: Temps moyen pour une première réponse
      avg_resolution_time: Temps nécessaire pour résoudre une demande (en moyenne)
      resolution_count: Nombre de résolutions
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Nom de la boîte de réception
      inbox_type: Type de boîte de réception
      conversations_count: Nbre de conversations
      avg_first_response_time: Temps moyen pour une première réponse
      avg_resolution_time: Temps nécessaire pour résoudre une demande (en moyenne)
    label_csv:
      label_title: Libellé
      conversations_count: Nbre de conversations
      avg_first_response_time: Temps moyen pour une première réponse
      avg_resolution_time: Temps nécessaire pour résoudre une demande (en moyenne)
      avg_reply_time: Avg reply time
      resolution_count: Nombre de résolutions
    team_csv:
      team_name: Nom de l'équipe
      conversations_count: Nombre de conversations
      avg_first_response_time: Temps moyen pour une première réponse
      avg_resolution_time: Temps nécessaire pour résoudre une demande (en moyenne)
      resolution_count: Nombre de résolutions
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Fuseau horaire
    sla_csv:
      conversation_id: Conversation ID
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: Équipes
      inbox: Boîte de réception
      labels: Étiquettes
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: jour
    csat:
      headers:
        contact_name: Nom du contact
        contact_email_address: Adresse e-mail du contact
        contact_phone_number: Numéro de téléphone du contact
        link_to_the_conversation: Lier à la conversation
        agent_name: Nom de l'agent
        rating: Note
        feedback: Commentaire
        recorded_at: Date d'enregistrement
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'A new message is created in conversation (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} vous a mentionné dans la story: '
      instagram_deleted_story_content: Cette Story n'est plus disponible.
      deleted: Ce message a été supprimé
      whatsapp:
        list_button_label: 'Choose an item'
      delivery_status:
        error_code: 'Code d''erreur : %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'La conversation a été marquée résolue par %{user_name}'
        contact_resolved: 'La conversation a été résolue par %{contact_name}'
        open: 'La conversation a été ré-ouverte par %{user_name}'
        pending: 'La conversation a été marquée comme en attente par %{user_name}'
        snoozed: 'La conversation a été reportée par %{user_name}'
        auto_resolved_days: 'La conversation a été marquée comme résolue par le système en raison de %{count} jours d''inactivité'
        auto_resolved_hours: 'La conversation a été marquée comme résolue par le système en raison de %{count} heures d''inactivité'
        auto_resolved_minutes: 'La conversation a été marquée comme résolue par le système en raison de %{count} minutes d''inactivité'
        system_auto_open: Le système a rouvert la conversation en raison d'un nouveau message entrant.
      priority:
        added: '%{user_name} fixe la priorité à %{new_priority}'
        updated: '%{user_name} a modifié la priorité de %{old_priority} à %{new_priority}'
        removed: '%{user_name} a supprimé la priorité'
      assignee:
        self_assigned: '%{user_name} s''est auto-assigné cette conversation'
        assigned: 'Assigné à %{assignee_name} par %{user_name}'
        removed: 'Responsable de la conversation supprimé par %{user_name}'
      team:
        assigned: 'Assigné à %{team_name} par %{user_name}'
        assigned_with_assignee: 'Assigné à %{assignee_name} via %{team_name} par %{user_name}'
        removed: 'Non assigné de %{team_name} par %{user_name}'
      labels:
        added: '%{user_name} a ajouté %{labels}'
        removed: '%{user_name} a supprimé %{labels}'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      linear:
        issue_created: 'Linear issue %{issue_id} was created by %{user_name}'
        issue_linked: 'Linear issue %{issue_id} was linked by %{user_name}'
        issue_unlinked: 'Linear issue %{issue_id} was unlinked by %{user_name}'
      csat:
        not_sent_due_to_messaging_window: 'CSAT survey not sent due to outgoing message restrictions'
      muted: '%{user_name} a mis la conversation en sourdine'
      unmuted: '%{user_name} a rétabli le son de la conversation'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} répond généralement en quelques heures.'
      ways_to_reach_you_message_body: 'Donnez à l''équipe un moyen de vous recontacter.'
      email_input_box_message_body: 'Recevez des notifications par courriel'
      csat_input_message_body: 'Veuillez évaluer la conversation'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} de %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} de %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} de %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} de %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Nouveaux messages dans cette conversation'
      transcript_subject: 'Transcription de conversation'
    survey:
      response: 'Merci de noter cette conversation, %{link}'
  contacts:
    online:
      delete: '%{contact_name} est en ligne, veuillez réessayer plus tard'
  integration_apps:
    #Note: webhooks and dashboard_apps don't need short_description as they use different modal components
    dashboard_apps:
      name: 'Applications du tableau de bord'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      short_description: 'Start video/voice calls with customers directly from Chatwoot.'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Chatwoot.'
      meeting_name: '%{agent_name} a démarré une réunion'
    slack:
      name: 'Slack'
      short_description: 'Receive notifications and respond to conversations directly in Slack.'
      description: "Integrate Chatwoot with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Webhooks'
      description: 'Webhook events provide real-time updates about activities in your Chatwoot account. You can subscribe to your preferred events, and Chatwoot will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow'
      short_description: 'Build chatbots to handle initial queries before transferring to agents.'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'Google Translate'
      short_description: 'Automatically translate customer messages for agents.'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      short_description: 'AI-powered reply suggestions, summarization, and message enhancement.'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      short_description: 'Create and link Linear issues directly from conversations.'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      short_description: 'Access order details and customer data from your Shopify store.'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Synchronisez vos contacts et conversations avec LeadSquared CRM.'
      description: 'Synchronisez vos contacts et conversations avec LeadSquared CRM. Cette intégration crée automatiquement des prospects dans LeadSquared lorsque de nouveaux contacts sont ajoutés, et enregistre l''activité des conversations pour fournir à votre équipe de vente un contexte complet.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
    copilot:
      using_tool: 'Using tool %{function_name}'
      completed_tool_call: 'Completed %{function_name} tool call'
      invalid_tool_call: 'Invalid tool call'
      tool_not_available: 'Tool not available'
  public_portal:
    search:
      search_placeholder: Rechercher un article par titre ou contenu...
      empty_placeholder: Aucun résultat trouvé.
      loading_placeholder: Recherche en cours...
      results_title: Résultats de recherche
    toc_header: 'Sur cette page'
    hero:
      sub_title: Recherchez les articles ici ou parcourez les catégories ci-dessous.
    common:
      home: Page d'accueil
      last_updated_on: Dernière mise à jour le %{last_updated_on}
      view_all_articles: Tout afficher
      article: article
      articles: articles
      author: auteur
      authors: auteurs
      other: autre
      others: autres
      by: Par
      no_articles: Il n'y a pas d'articles ici
    footer:
      made_with: Réalisé avec
    header:
      go_to_homepage: Site internet
      visit_website: Visit website
      appearance:
        system: Système
        light: Clair
        dark: Sombre
      featured_articles: Articles à la une
      uncategorized: Non catégorisé
    404:
      title: Page introuvable
      description: Nous n'avons pas pu trouver la page que vous cherchiez.
      back_to_home: Aller à la page d'accueil
  slack_unfurl:
    fields:
      name: Nom
      email: Courriel
      phone_number: Téléphone
      company_name: Société
      inbox_name: Boîte de réception
      inbox_type: Type de boîte de réception
    button: Ouvrir la conversation
  time_units:
    days:
      one: '%{count} jour'
      other: '%{count} days'
    hours:
      one: '%{count} heure'
      other: '%{count} hours'
    minutes:
      one: '%{count} minute'
      other: '%{count} minutes'
    seconds:
      one: '%{count} seconde'
      other: '%{count} seconds'
  automation:
    system_name: 'Système d''automatisation'
  crm:
    no_message: 'Aucun message dans la conversation'
    attachment: '[Pièce jointe : %{type}]'
    no_content: '[No content]'
    created_activity: |
      Une nouvelle conversation a été lancée sur %{brand_name}

      Canal : %{channel_info}
      Créée le : %{formatted_creation_time}
      ID de la conversation : %{display_id}
      Voir dans %{brand_name} : %{url}
    transcript_activity: |
      Transcription de la conversation depuis %{brand_name}

      Canal : %{channel_info}
      ID de la conversation : %{display_id}
      Voir dans %{brand_name} : %{url}

      Transcription :
      %{format_messages}
