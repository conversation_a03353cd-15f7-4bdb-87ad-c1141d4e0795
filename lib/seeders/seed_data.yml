company:
  name: '<PERSON><PERSON>ayer'
  domain: 'paperlayer.test'
users:
  - name: '<PERSON>'
    gender: male
    email: 'mi<PERSON><PERSON><EMAIL>'
    team:
      - 'sales'
      - 'management'
      - 'administration'
      - 'warehouse'
    role: 'administrator'
  - name: '<PERSON>'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON><PERSON><PERSON>'
    gender: male
    email: '<PERSON><PERSON><PERSON>@paperlayer.test'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: male
    email: 't<PERSON>@paperlayer.test'
    team:
      - 'Management'
  - name: '<PERSON>'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Sales'
    custom_role: 'Sales Representative'
  - name: '<PERSON>'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Sales'
    custom_role: 'Customer Support Lead'
  - name: 'Ben Nugent'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Sales'
    custom_role: 'Junior Agent'
  - name: 'Todd Packer'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Sales'
    custom_role: 'Sales Representative'
  - name: '<PERSON> Simms'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Administration'
    custom_role: 'Knowledge Manager'
  - name: 'Hunter Jo'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Administration'
    custom_role: 'Analytics Specialist'
  - name: 'Rolando Silva'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Administration'
    custom_role: 'Junior Agent'
  - name: 'Stephanie Wilson'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Administration'
    custom_role: 'Escalation Handler'
  - name: 'Jordan Garfield'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Administration'
  - name: 'Ronni Carlo'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Administration'
  - name: 'Lonny Collins'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Warehouse'
    custom_role: 'Customer Support Lead'
  - name: 'Madge Madsen'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Glenn Max'
    gender: female
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Jerry DiCanio'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Phillip Martin'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Michael Josh'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Matt Hudson'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Gideon'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Bruce'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Frank'
    gender: male
    email: '<EMAIL>'
    team:
      - 'Warehouse'
  - name: 'Louanne Kelley'
    gender: female
    email: '<EMAIL>'
  - name: 'Devon White'
    gender: male
    email: '<EMAIL>'
    custom_role: 'Escalation Handler'
  - name: 'Kendall'
    gender: male
    email: '<EMAIL>'
  - email: '<EMAIL>'
    name: 'Sadiq'
    gender: male
teams:
  - '💰 Sales'
  - '💼 Management'
  - '👩‍💼 Administration'
  - '🚛 Warehouse'
custom_roles:
  - name: 'Customer Support Lead'
    description: 'Lead support agent with full conversation and contact management'
    permissions:
      - 'conversation_manage'
      - 'contact_manage'
      - 'report_manage'
  - name: 'Sales Representative'
    description: 'Sales team member with conversation and contact access'
    permissions:
      - 'conversation_unassigned_manage'
      - 'conversation_participating_manage'
      - 'contact_manage'
  - name: 'Knowledge Manager'
    description: 'Manages knowledge base and participates in conversations'
    permissions:
      - 'knowledge_base_manage'
      - 'conversation_participating_manage'
  - name: 'Junior Agent'
    description: 'Entry-level agent with basic conversation access'
    permissions:
      - 'conversation_participating_manage'
  - name: 'Analytics Specialist'
    description: 'Focused on reports and data analysis'
    permissions:
      - 'report_manage'
      - 'conversation_participating_manage'
  - name: 'Escalation Handler'
    description: 'Handles unassigned conversations and escalations'
    permissions:
      - 'conversation_unassigned_manage'
      - 'conversation_participating_manage'
      - 'contact_manage'
labels:
    - title: 'billing'
      color: '#28AD21'
      show_on_sidebar: true
    - title: 'software'
      color: '#8F6EF2'
      show_on_sidebar: true
    - title: 'delivery'
      color: '#A2FDD5'
      show_on_sidebar: true
    - title: 'ops-handover'
      color: '#A53326'
      show_on_sidebar: true
    - title: 'premium-customer'
      color: '#6FD4EF'
      show_on_sidebar: true
    - title: 'lead'
      color: '#F161C8'
      show_on_sidebar: true
contacts:
    - name: "Lorrie Trosdall"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::WebWidget
          messages:
          - message_type: incoming
            content: Hi, I'm having trouble logging in to my account.
          - message_type: outgoing
            sender: <EMAIL>
            content: Hi! Sorry to hear that. Can you please provide me with your username and email address so I can look into it for you?
    - name: "Tiffanie Cloughton"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::FacebookPage
          messages:
          - message_type: incoming
            content: Hi, I need some help with my billing statement.
          - message_type: outgoing
            sender: <EMAIL>
            content: Hello! I'd be happy to assist you with that. Can you please tell me which billing statement you're referring to?
    - name: "Melonie Keatch"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::TwitterProfile
          messages:
          - message_type: incoming
            content: Hi, I think I accidentally deleted some important files. Can you help me recover them?
          - message_type: outgoing
            sender: <EMAIL>
            content: Of course! Can you please tell me what type of files they were and where they were located on your device?
    - name: "Olin Canniffe"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::Whatsapp
          source_id: "123456723"
          messages:
          - message_type: incoming
            content: Hi, I'm having trouble connecting to the internet.
          - message_type: outgoing
            sender: <EMAIL>
            content: I'm sorry to hear that. Have you tried restarting your modem/router? If that doesn't work, please let me know and I can provide further assistance.
    - name: "Viviene Corp"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::Sms
          source_id: "+1234567"
          messages:
          - message_type: incoming
            content: Hi, I'm having trouble with the mobile app. It keeps crashing.
          - message_type: outgoing
            sender: <EMAIL>
            content: I'm sorry to hear that. Can you please try uninstalling and reinstalling the app and see if that helps? If not, please let me know and I can look into it further.
    - name: "Drake Pittway"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::Line
          messages:
          - message_type: incoming
            content: Hi, I'm trying to update my account information but it won't save.
          - message_type: outgoing
            sender: <EMAIL>
            content: Sorry for the inconvenience. Can you please provide me with the specific information you're trying to update and the error message you're receiving?
    - name: "Klaus Crawley"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::WebWidget
          messages:
          - message_type: incoming
            content: Hi, I need some help setting up my new device.
          - message_type: outgoing
            sender: <EMAIL>
            content: No problem! Can you please tell me the make and model of your device and what specifically you need help with?
    - name: "Bing Cusworth"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::TwitterProfile
          messages:
          - message_type: incoming
            content: Hi, I accidentally placed an order for the wrong item. Can I cancel it?
          - message_type: outgoing
            sender: <EMAIL>
            content: I'm sorry to hear that. Can you please provide me with your order number and I'll see if I can cancel it for you?
    - name: "Claus Jira"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::Whatsapp
          source_id: "12323432"
          messages:
          - message_type: incoming
            content: Hi, I'm having trouble with my email. I can't seem to send or receive any messages.
          - message_type: outgoing
            sender: <EMAIL>
            content: I'm sorry to hear that. Can you please tell me what email client you're using and if you're receiving any error messages?
    - name: "Quent Dalliston"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::Whatsapp
          source_id: "12342234324"
          messages:
          - message_type: incoming
            content: Hi, I need some help resetting my password.
          - message_type: outgoing
            sender: <EMAIL>
            content: Sure! Can you please provide me with your username or email address and I'll send you a password reset link?
    - name: "Coreen Mewett"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::FacebookPage
          messages:
          - message_type: incoming
            content: Hi, I think someone may have hacked into my account. What should I do?
          - message_type: outgoing
            sender: <EMAIL>
            content: I'm sorry to hear that. Please change your password immediately and enable two-factor authentication if you haven't already done so. I can also assist you in reviewing your account activity if needed.
    - name: "Benyamin Janeway"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::Line
          messages:
          - message_type: incoming
            content: Hi, I have a question about your product features.
          - message_type: outgoing
            sender: <EMAIL>
            content: Sure thing! What specific feature are you interested in learning more about?
    - name: "Cordell Dalinder"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::Email
          source_id: "<EMAIL>"
          messages:
          - message_type: incoming
            content: Hi, I need help setting up my new printer.
          - message_type: outgoing
            sender: <EMAIL>
            content: No problem! Can you please provide me with the make and model of your printer and what type of device you'll be connecting it to?
    - name: "Merrile Petruk"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::Email
          source_id: "<EMAIL>"
          priority: urgent
          messages:
          - message_type: incoming
            content: Hi, I'm having trouble accessing a file that I shared with someone.
          - message_type: outgoing
            sender: <EMAIL>
            content: I'm sorry to hear that. Can you please tell me which file you're having trouble accessing and who you shared it with? I'll do my best to help you regain access.
    - name: "Nathaniel Vannuchi"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::FacebookPage
          priority: high
          messages:
            - message_type: incoming
              content: "Hey there,I need some help with billing, my card is not working on the website."
    - name: "Olia Olenchenko"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::WebWidget
          priority: high
          assignee: <EMAIL>
          messages:
            - message_type: incoming
              content: "Billing section is not working, it throws some error."
    - name: "Elisabeth Derington"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::Whatsapp
          priority: high
          source_id: "1223423567"
          labels:
            - billing
            - delivery
            - ops-handover
            - premium-customer
          messages:
            - message_type: incoming
              content: "Hey \n I didn't get the product delivered, but it shows it is delivered to my address. Please check"
    - name: "Willy Castelot"
      email: "<EMAIL>"
      gender: 'male'
      conversations:
        - channel: Channel::WebWidget
          priority: medium
          labels:
            - software
            - ops-handover
          messages:
            - message_type: incoming
              content: "Hey there, \n I need some help with the product, my button is not working on the website."
    - name: "Ophelia Folkard"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::WebWidget
          priority: low
          assignee: <EMAIL>
          labels:
            - billing
            - software
            - lead
          messages:
            - message_type: incoming
              content: "Hey, \n My card is not working on your website. Please help"
    - name: "Candice Matherson"
      email: "<EMAIL>"
      gender: 'female'
      conversations:
        - channel: Channel::Email
          priority: urgent
          source_id: "<EMAIL>"
          assignee: <EMAIL>
          labels:
            - billing
            - lead
          messages:
            - message_type: incoming
              content: "Hey, \n I'm looking for some help to figure out if it is the right product for me."
            - message_type: outgoing
              sender: <EMAIL>
              content: Welcome to PaperLayer. Our Team will be getting back you shortly.
            - message_type: outgoing
              sender: <EMAIL>
              content: How may i help you ?
              sender: <EMAIL>
